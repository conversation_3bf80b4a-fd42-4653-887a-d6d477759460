#!/usr/bin/env node

/**
 * ESP32-S3 USB HID 测试客户端
 * 用于自动化测试ESP32-S3的HID功能
 */

const net = require('net');

class HIDTestClient {
    constructor(host = 'localhost', port = 8080) {
        this.host = host;
        this.port = port;
        this.client = null;
        this.connected = false;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.client = new net.Socket();
            
            this.client.connect(this.port, this.host, () => {
                console.log(`✓ 连接到服务器: ${this.host}:${this.port}`);
                this.connected = true;
                resolve();
            });

            this.client.on('data', (data) => {
                console.log(`收到服务器响应: ${data.toString().trim()}`);
            });

            this.client.on('close', () => {
                console.log('✗ 与服务器断开连接');
                this.connected = false;
            });

            this.client.on('error', (err) => {
                console.log(`连接错误: ${err.message}`);
                this.connected = false;
                reject(err);
            });
        });
    }

    sendCommand(command) {
        if (!this.connected) {
            console.log('❌ 未连接到服务器');
            return false;
        }

        try {
            this.client.write(command + '\n');
            console.log(`📤 发送命令: ${command}`);
            return true;
        } catch (error) {
            console.log(`❌ 发送失败: ${error.message}`);
            return false;
        }
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runKeyboardTests() {
        console.log('\n🎹 开始键盘功能测试...');
        
        const keyboardTests = [
            { cmd: 'key.a', desc: '单个字母按键' },
            { cmd: 'key.space', desc: '空格键' },
            { cmd: 'key.enter', desc: '回车键' },
            { cmd: 'key.backspace', desc: '退格键' },
            { cmd: 'key.tab', desc: 'Tab键' },
            { cmd: 'key.esc', desc: 'Esc键' },
            { cmd: 'key.f1', desc: '功能键F1' },
            { cmd: 'key.up', desc: '上箭头键' },
            { cmd: 'key.ctrl+c', desc: 'Ctrl+C组合键' },
            { cmd: 'key.alt+tab', desc: 'Alt+Tab组合键' },
            { cmd: 'key.shift+a', desc: 'Shift+A组合键' },
            { cmd: 'key.ctrl+shift+esc', desc: '三键组合' }
        ];

        let successCount = 0;
        for (const test of keyboardTests) {
            console.log(`测试: ${test.desc}`);
            if (this.sendCommand(test.cmd)) {
                successCount++;
            }
            await this.delay(1000);
        }

        console.log(`\n键盘测试完成: ${successCount}/${keyboardTests.length} 成功`);
        return successCount === keyboardTests.length;
    }

    async runMouseTests() {
        console.log('\n🖱️ 开始鼠标功能测试...');
        
        const mouseTests = [
            { cmd: 'mouse.click,left', desc: '左键点击' },
            { cmd: 'mouse.click,right', desc: '右键点击' },
            { cmd: 'mouse.click,middle', desc: '中键点击' },
            { cmd: 'mouse.press,left', desc: '按下左键' },
            { cmd: 'mouse.release,left', desc: '释放左键' },
            { cmd: 'mouse.move,100,50', desc: '鼠标移动(正值)' },
            { cmd: 'mouse.move,-50,30', desc: '鼠标移动(负值)' },
            { cmd: 'mouse.scroll,0,0,3', desc: '向上滚动' },
            { cmd: 'mouse.scroll,0,0,-2', desc: '向下滚动' }
        ];

        let successCount = 0;
        for (const test of mouseTests) {
            console.log(`测试: ${test.desc}`);
            if (this.sendCommand(test.cmd)) {
                successCount++;
            }
            await this.delay(1000);
        }

        console.log(`\n鼠标测试完成: ${successCount}/${mouseTests.length} 成功`);
        return successCount === mouseTests.length;
    }

    async runInteractiveDemo() {
        console.log('\n🎮 开始交互式演示...');
        
        const demoSequence = [
            { cmd: 'key.ctrl+a', desc: '全选文本', delay: 1500 },
            { cmd: 'key.ctrl+c', desc: '复制文本', delay: 1000 },
            { cmd: 'key.right', desc: '移动光标', delay: 500 },
            { cmd: 'key.ctrl+v', desc: '粘贴文本', delay: 1000 },
            { cmd: 'mouse.move,100,0', desc: '鼠标右移', delay: 800 },
            { cmd: 'mouse.click,left', desc: '左键点击', delay: 800 },
            { cmd: 'mouse.scroll,0,0,3', desc: '向上滚动', delay: 800 },
            { cmd: 'mouse.move,-100,0', desc: '鼠标左移', delay: 800 }
        ];

        for (const step of demoSequence) {
            console.log(`执行: ${step.desc}`);
            this.sendCommand(step.cmd);
            await this.delay(step.delay);
        }

        console.log('✅ 交互式演示完成!');
    }

    async runStressTest() {
        console.log('\n⚡ 开始压力测试...');
        
        const commands = [
            'key.a', 'key.b', 'key.c',
            'mouse.click,left', 'mouse.move,10,10',
            'key.space', 'mouse.scroll,0,0,1'
        ];

        const testCount = 50;
        let successCount = 0;

        console.log(`发送 ${testCount} 个快速命令...`);
        
        for (let i = 0; i < testCount; i++) {
            const cmd = commands[i % commands.length];
            if (this.sendCommand(cmd)) {
                successCount++;
            }
            await this.delay(100); // 快速发送
        }

        console.log(`压力测试完成: ${successCount}/${testCount} 成功`);
        return successCount === testCount;
    }

    disconnect() {
        if (this.client) {
            this.client.destroy();
        }
    }
}

async function main() {
    const args = process.argv.slice(2);
    const host = args[0] || 'localhost';
    const port = parseInt(args[1]) || 8080;

    console.log('🚀 ESP32-S3 HID 自动化测试客户端');
    console.log(`连接目标: ${host}:${port}`);

    const client = new HIDTestClient(host, port);

    try {
        await client.connect();
        
        console.log('\n选择测试模式:');
        console.log('1. 完整测试 (键盘 + 鼠标)');
        console.log('2. 仅键盘测试');
        console.log('3. 仅鼠标测试');
        console.log('4. 交互式演示');
        console.log('5. 压力测试');

        // 如果有命令行参数指定测试模式
        const testMode = args[2] || '1';
        
        let keyboardOk = true;
        let mouseOk = true;
        
        switch (testMode) {
            case '1':
                console.log('\n🧪 运行完整测试...');
                keyboardOk = await client.runKeyboardTests();
                await client.delay(2000);
                mouseOk = await client.runMouseTests();
                break;
                
            case '2':
                console.log('\n⌨️ 运行键盘测试...');
                keyboardOk = await client.runKeyboardTests();
                mouseOk = true; // 跳过鼠标测试
                break;
                
            case '3':
                console.log('\n🖱️ 运行鼠标测试...');
                mouseOk = await client.runMouseTests();
                keyboardOk = true; // 跳过键盘测试
                break;
                
            case '4':
                console.log('\n🎮 运行交互式演示...');
                await client.runInteractiveDemo();
                break;
                
            case '5':
                console.log('\n⚡ 运行压力测试...');
                await client.runStressTest();
                break;
                
            default:
                console.log('❌ 无效的测试模式');
                break;
        }

        if (testMode === '1' || testMode === '2' || testMode === '3') {
            console.log('\n📊 测试结果:');
            if (keyboardOk && mouseOk) {
                console.log('🎉 所有测试通过!');
            } else {
                console.log('❌ 部分测试失败');
                if (!keyboardOk) console.log('  - 键盘测试失败');
                if (!mouseOk) console.log('  - 鼠标测试失败');
            }
        }

    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        console.log('\n请确保:');
        console.log('1. 测试服务器正在运行');
        console.log('2. ESP32-S3已连接到服务器');
        console.log('3. 网络连接正常');
    } finally {
        client.disconnect();
        console.log('\n测试完成');
    }
}

// 显示使用帮助
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('ESP32-S3 HID 测试客户端');
    console.log('');
    console.log('用法:');
    console.log('  node test_client.js [host] [port] [test_mode]');
    console.log('');
    console.log('参数:');
    console.log('  host       服务器地址 (默认: localhost)');
    console.log('  port       服务器端口 (默认: 8080)');
    console.log('  test_mode  测试模式 1-5 (默认: 1)');
    console.log('');
    console.log('测试模式:');
    console.log('  1 - 完整测试');
    console.log('  2 - 仅键盘测试');
    console.log('  3 - 仅鼠标测试');
    console.log('  4 - 交互式演示');
    console.log('  5 - 压力测试');
    console.log('');
    console.log('示例:');
    console.log('  node test_client.js                    # 本地完整测试');
    console.log('  node test_client.js *************      # 远程完整测试');
    console.log('  node test_client.js localhost 8080 2   # 本地键盘测试');
    process.exit(0);
}

// 运行主程序
main().catch(console.error);
