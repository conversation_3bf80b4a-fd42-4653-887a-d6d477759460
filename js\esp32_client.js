#!/usr/bin/env node

/**
 * ESP32-S3 Socket客户端 (Node.js版本)
 * 连接到ESP32-S3服务器发送HID命令
 */

const net = require('net');
const readline = require('readline');

class ESP32Client {
    constructor(esp32IP, port = 8080) {
        this.esp32IP = esp32IP;
        this.port = port;
        this.socket = null;
        this.connected = false;
        this.rl = null;
    }

    connect() {
        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();
            
            this.socket.connect(this.port, this.esp32IP, () => {
                this.connected = true;
                console.log(`✓ 已连接到ESP32-S3: ${this.esp32IP}:${this.port}`);
                resolve();
            });

            this.socket.on('data', (data) => {
                console.log(`收到ESP32-S3响应: ${data.toString().trim()}`);
            });

            this.socket.on('close', () => {
                console.log('✗ 与ESP32-S3断开连接');
                this.connected = false;
            });

            this.socket.on('error', (err) => {
                console.log(`❌ 连接错误: ${err.message}`);
                this.connected = false;
                reject(err);
            });
        });
    }

    sendCommand(command) {
        if (!this.connected) {
            console.log('❌ 未连接到ESP32-S3');
            return false;
        }

        try {
            this.socket.write(command + '\n');
            console.log(`📤 发送: ${command}`);
            return true;
        } catch (error) {
            console.log(`❌ 发送失败: ${error.message}`);
            this.connected = false;
            return false;
        }
    }

    disconnect() {
        if (this.socket) {
            this.socket.destroy();
            this.connected = false;
            console.log('✗ 已断开连接');
        }
    }

    async interactiveMode() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        console.log('\n=== ESP32-S3 HID 控制器 ===');
        console.log('命令示例:');
        console.log('  键盘: key.a, key.ctrl+c, key.alt+tab');
        console.log('  鼠标: mouse.click,left, mouse.move,100,50');
        console.log('  输入 "help" 查看帮助, "quit" 退出');
        console.log('========================\n');

        const askCommand = () => {
            this.rl.question('输入命令: ', (command) => {
                command = command.trim();

                if (command.toLowerCase() === 'quit') {
                    this.rl.close();
                    this.disconnect();
                    return;
                }

                if (command.toLowerCase() === 'help') {
                    this.showHelp();
                } else if (command) {
                    this.sendCommand(command);
                }

                if (this.connected) {
                    askCommand();
                }
            });
        };

        askCommand();
    }

    showHelp() {
        console.log('\n=== 命令帮助 ===');
        console.log('键盘命令:');
        console.log('  key.a                 - 按下字母a');
        console.log('  key.ctrl+c            - Ctrl+C组合键');
        console.log('  key.alt+tab           - Alt+Tab组合键');
        console.log('  key.enter             - 回车键');
        console.log('  key.space             - 空格键');
        console.log('  key.f1                - 功能键F1');
        console.log('  key.up                - 上箭头键');
        console.log('');
        console.log('鼠标命令:');
        console.log('  mouse.click,left      - 左键点击');
        console.log('  mouse.click,right     - 右键点击');
        console.log('  mouse.click,middle    - 中键点击');
        console.log('  mouse.press,left      - 按下左键');
        console.log('  mouse.release,left    - 释放左键');
        console.log('  mouse.move,100,50     - 移动鼠标');
        console.log('  mouse.scroll,0,0,3    - 向上滚动');
        console.log('================\n');
    }

    async runDemo() {
        console.log('\n🎮 运行演示序列...');
        
        const demoCommands = [
            { cmd: 'key.a', desc: '输入字母a', delay: 1000 },
            { cmd: 'key.space', desc: '按空格', delay: 1000 },
            { cmd: 'key.ctrl+a', desc: '全选', delay: 1500 },
            { cmd: 'key.ctrl+c', desc: '复制', delay: 1000 },
            { cmd: 'mouse.click,left', desc: '左键点击', delay: 1000 },
            { cmd: 'mouse.move,100,50', desc: '移动鼠标', delay: 1000 },
            { cmd: 'mouse.scroll,0,0,3', desc: '向上滚动', delay: 1000 },
            { cmd: 'key.ctrl+v', desc: '粘贴', delay: 1000 }
        ];

        for (const step of demoCommands) {
            console.log(`执行: ${step.desc}`);
            this.sendCommand(step.cmd);
            await this.delay(step.delay);
        }

        console.log('✅ 演示完成!');
    }

    async runTest() {
        console.log('\n🧪 运行功能测试...');
        
        const testCommands = [
            'key.a', 'key.enter', 'key.space', 'key.ctrl+c',
            'mouse.click,left', 'mouse.move,50,50', 'mouse.scroll,0,0,2'
        ];

        let successCount = 0;
        for (const cmd of testCommands) {
            console.log(`测试: ${cmd}`);
            if (this.sendCommand(cmd)) {
                successCount++;
            }
            await this.delay(800);
        }

        console.log(`\n测试完成: ${successCount}/${testCommands.length} 成功`);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

async function main() {
    const args = process.argv.slice(2);
    
    if (args.length < 1) {
        console.log('用法: node esp32_client.js <ESP32_IP> [port] [mode]');
        console.log('示例:');
        console.log('  node esp32_client.js ***************');
        console.log('  node esp32_client.js *************** 8080');
        console.log('  node esp32_client.js *************** 8080 demo');
        console.log('');
        console.log('模式:');
        console.log('  interactive (默认) - 交互模式');
        console.log('  demo              - 演示模式');
        console.log('  test              - 测试模式');
        process.exit(1);
    }

    const esp32IP = args[0];
    const port = parseInt(args[1]) || 8080;
    const mode = args[2] || 'interactive';

    console.log('🚀 ESP32-S3 客户端');
    console.log(`目标: ${esp32IP}:${port}`);

    const client = new ESP32Client(esp32IP, port);

    try {
        await client.connect();
        
        switch (mode) {
            case 'demo':
                await client.runDemo();
                client.disconnect();
                break;
            case 'test':
                await client.runTest();
                client.disconnect();
                break;
            default:
                await client.interactiveMode();
                break;
        }
    } catch (error) {
        console.log('\n请确保:');
        console.log('1. ESP32-S3已连接到WiFi');
        console.log('2. ESP32-S3固件正在运行');
        console.log('3. IP地址正确');
        console.log('4. 防火墙允许连接');
    }
}

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n程序退出');
    process.exit(0);
});

main().catch(console.error);
