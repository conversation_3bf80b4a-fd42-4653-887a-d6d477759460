#!/usr/bin/env python3
"""
Test server for ESP32-S3 USB HID remote control
This server accepts connections and allows you to send commands to the ESP32-S3
"""

import socket
import threading
import time

class HIDTestServer:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.clients = []
        self.running = False
        
    def start(self):
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(5)
        
        print(f"Server listening on {self.host}:{self.port}")
        print("Waiting for ESP32-S3 connections...")
        
        self.running = True
        
        # Start command input thread
        command_thread = threading.Thread(target=self.command_input_loop)
        command_thread.daemon = True
        command_thread.start()
        
        try:
            while self.running:
                client_socket, address = self.server_socket.accept()
                print(f"Connection from {address}")
                
                client_thread = threading.Thread(
                    target=self.handle_client, 
                    args=(client_socket, address)
                )
                client_thread.daemon = True
                client_thread.start()
                
        except KeyboardInterrupt:
            print("\nShutting down server...")
            self.running = False
            
    def handle_client(self, client_socket, address):
        self.clients.append(client_socket)
        try:
            while self.running:
                # Keep connection alive
                time.sleep(1)
        except:
            pass
        finally:
            if client_socket in self.clients:
                self.clients.remove(client_socket)
            client_socket.close()
            print(f"Client {address} disconnected")
            
    def send_command(self, command):
        """Send command to all connected clients"""
        if not self.clients:
            print("No clients connected")
            return
            
        disconnected_clients = []
        for client in self.clients:
            try:
                client.send((command + '\n').encode())
                print(f"Sent: {command}")
            except:
                disconnected_clients.append(client)
                
        # Remove disconnected clients
        for client in disconnected_clients:
            self.clients.remove(client)
            
    def command_input_loop(self):
        """Interactive command input loop"""
        print("\n=== ESP32-S3 HID Remote Control ===")
        print("Commands:")
        print("  Keyboard: key.a, key.ctrl+c, key.alt+tab")
        print("  Mouse: mouse.click,left, mouse.move,100,50, mouse.scroll,0,0,3")
        print("  Type 'help' for more examples, 'quit' to exit")
        print("=====================================\n")
        
        while self.running:
            try:
                command = input("Enter command: ").strip()
                
                if command.lower() == 'quit':
                    self.running = False
                    break
                elif command.lower() == 'help':
                    self.show_help()
                elif command:
                    self.send_command(command)
                    
            except KeyboardInterrupt:
                self.running = False
                break
                
    def show_help(self):
        print("\n=== Command Examples ===")
        print("Keyboard commands:")
        print("  key.a                 - Press 'a' key")
        print("  key.ctrl+c            - Ctrl+C combination")
        print("  key.alt+tab           - Alt+Tab combination")
        print("  key.shift+a           - Shift+A (capital A)")
        print("  key.enter             - Enter key")
        print("  key.space             - Space key")
        print("  key.f1                - Function key F1")
        print("  key.up                - Arrow up key")
        print("")
        print("Mouse commands:")
        print("  mouse.click,left      - Left click")
        print("  mouse.click,right     - Right click")
        print("  mouse.click,middle    - Middle click")
        print("  mouse.press,left      - Press and hold left button")
        print("  mouse.release,left    - Release left button")
        print("  mouse.move,100,50     - Move mouse by x=100, y=50")
        print("  mouse.scroll,0,0,3    - Scroll wheel up by 3")
        print("  mouse.scroll,0,0,-2   - Scroll wheel down by 2")
        print("========================\n")

if __name__ == "__main__":
    server = HIDTestServer()
    server.start()
