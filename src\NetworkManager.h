#ifndef NETWORK_MANAGER_H
#define NETWORK_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiServer.h>
#include <WiFiClient.h>

class NetworkManager {
private:
    const char* ssid;
    const char* password;
    int server_port;

    WiFiServer* server;
    WiFiClient client;
    bool clientConnected = false;
    unsigned long lastConnectionAttempt = 0;
    const unsigned long connectionRetryInterval = 5000; // 5 seconds

    String receivedCommand = "";

    void connectToWiFi();
    void handleClientConnections();
    void readFromClient();

public:
    void init(const char* wifi_ssid, const char* wifi_password, int srv_port);
    void handleConnection();
    String getReceivedCommand();
    bool isConnected();
    String getIPAddress();
};

#endif
