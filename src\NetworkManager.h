#ifndef NETWORK_MANAGER_H
#define NETWORK_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiClient.h>

class NetworkManager {
private:
    const char* ssid;
    const char* password;
    const char* server_ip;
    int server_port;
    
    WiFiClient client;
    bool connected = false;
    unsigned long lastConnectionAttempt = 0;
    const unsigned long connectionRetryInterval = 5000; // 5 seconds
    
    String receivedCommand = "";
    
    void connectToWiFi();
    void connectToServer();
    void readFromServer();
    
public:
    void init(const char* wifi_ssid, const char* wifi_password, 
              const char* srv_ip, int srv_port);
    void handleConnection();
    String getReceivedCommand();
    bool isConnected();
};

#endif
