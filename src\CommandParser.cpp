#include "CommandParser.h"

void CommandParser::init(USBHIDKeyboard* kb, USBHIDMouse* ms) {
    keyboard = kb;
    mouse = ms;
}

void CommandParser::parseAndExecute(String command) {
    command.trim();
    
    if (command.startsWith("key.")) {
        parseKeyboardCommand(command.substring(4));
    } else if (command.startsWith("mouse.")) {
        parseMouseCommand(command.substring(6));
    } else {
        Serial.println("Unknown command format: " + command);
    }
}

void CommandParser::parseKeyboardCommand(String command) {
    // Handle key combinations (e.g., "ctrl+a", "alt+tab")
    if (command.indexOf('+') != -1) {
        sendKeyCombo(command);
    } else {
        // Single key press
        sendKeyPress(command);
    }
}

void CommandParser::sendKeyPress(String key) {
    key.toLowerCase();
    
    uint8_t keyCode = getKeyCode(key);
    if (keyCode != 0) {
        keyboard.press(keyCode);
        delay(50);
        keyboard.release(keyCode);
        Serial.println("Key pressed: " + key);
    } else {
        // Try as a character
        if (key.length() == 1) {
            char c = key.charAt(0);
            keyboard.press(c);
            delay(50);
            keyboard.release(c);
            Serial.println("Character pressed: " + key);
        } else {
            Serial.println("Unknown key: " + key);
        }
    }
}

void CommandParser::sendKeyCombo(String combo) {
    combo.toLowerCase();
    
    // Split by '+' to get individual keys
    int startIndex = 0;
    int endIndex = combo.indexOf('+');
    
    // Press all modifier keys first
    while (endIndex != -1) {
        String modifier = combo.substring(startIndex, endIndex);
        modifier.trim();
        
        uint8_t modKey = getModifierKey(modifier);
        if (modKey != 0) {
            keyboard.press(modKey);
            Serial.println("Modifier pressed: " + modifier);
        }
        
        startIndex = endIndex + 1;
        endIndex = combo.indexOf('+', startIndex);
    }
    
    // Press the final key
    String finalKey = combo.substring(startIndex);
    finalKey.trim();
    
    uint8_t keyCode = getKeyCode(finalKey);
    if (keyCode != 0) {
        keyboard.press(keyCode);
        Serial.println("Final key pressed: " + finalKey);
    } else if (finalKey.length() == 1) {
        char c = finalKey.charAt(0);
        keyboard.press(c);
        Serial.println("Final character pressed: " + finalKey);
    }
    
    delay(100);  // Hold the combination
    
    // Release all keys
    keyboard.releaseAll();
    Serial.println("Key combination released: " + combo);
}

uint8_t CommandParser::getKeyCode(String key) {
    key.toLowerCase();
    
    if (key == "ctrl" || key == "control") return KEY_CTRL_LEFT;
    if (key == "shift") return KEY_SHIFT_LEFT;
    if (key == "alt") return KEY_ALT_LEFT;
    if (key == "win" || key == "cmd" || key == "gui") return KEY_GUI_LEFT;
    if (key == "up") return KEY_UP_ARROW;
    if (key == "down") return KEY_DOWN_ARROW;
    if (key == "left") return KEY_LEFT_ARROW;
    if (key == "right") return KEY_RIGHT_ARROW;
    if (key == "backspace") return KEY_BACKSPACE;
    if (key == "tab") return KEY_TAB;
    if (key == "enter" || key == "return") return KEY_RETURN;
    if (key == "esc" || key == "escape") return KEY_ESC;
    if (key == "insert") return KEY_INSERT;
    if (key == "delete" || key == "del") return KEY_DELETE;
    if (key == "pageup") return KEY_PAGE_UP;
    if (key == "pagedown") return KEY_PAGE_DOWN;
    if (key == "home") return KEY_HOME;
    if (key == "end") return KEY_END;
    if (key == "capslock") return KEY_CAPS_LOCK;
    if (key == "f1") return KEY_F1;
    if (key == "f2") return KEY_F2;
    if (key == "f3") return KEY_F3;
    if (key == "f4") return KEY_F4;
    if (key == "f5") return KEY_F5;
    if (key == "f6") return KEY_F6;
    if (key == "f7") return KEY_F7;
    if (key == "f8") return KEY_F8;
    if (key == "f9") return KEY_F9;
    if (key == "f10") return KEY_F10;
    if (key == "f11") return KEY_F11;
    if (key == "f12") return KEY_F12;
    if (key == "space") return ' ';
    
    return 0;  // Unknown key
}

uint8_t CommandParser::getModifierKey(String key) {
    key.toLowerCase();

    if (key == "ctrl" || key == "control") return KEY_CTRL_LEFT;
    if (key == "shift") return KEY_SHIFT_LEFT;
    if (key == "alt") return KEY_ALT_LEFT;
    if (key == "win" || key == "cmd" || key == "gui") return KEY_GUI_LEFT;

    return 0;  // Not a modifier key
}

void CommandParser::parseMouseCommand(String command) {
    // Format: mouse.action,x,y,wheel
    // Examples:
    // mouse.click,left
    // mouse.press,left
    // mouse.release,left
    // mouse.move,100,50
    // mouse.scroll,0,0,3
    // mouse.side1,press

    int firstComma = command.indexOf(',');
    if (firstComma == -1) {
        Serial.println("Invalid mouse command format: " + command);
        return;
    }

    String action = command.substring(0, firstComma);
    String params = command.substring(firstComma + 1);

    action.toLowerCase();

    if (action == "click") {
        // Single click
        handleMouseButton("click", params);
    } else if (action == "press") {
        // Press and hold
        handleMouseButton("press", params);
    } else if (action == "release") {
        // Release button
        handleMouseButton("release", params);
    } else if (action == "move") {
        // Move mouse
        int secondComma = params.indexOf(',');
        if (secondComma != -1) {
            int x = params.substring(0, secondComma).toInt();
            int y = params.substring(secondComma + 1).toInt();
            handleMouseMove(x, y);
        }
    } else if (action == "scroll") {
        // Scroll wheel
        int secondComma = params.indexOf(',');
        int thirdComma = params.indexOf(',', secondComma + 1);
        if (thirdComma != -1) {
            int wheel = params.substring(thirdComma + 1).toInt();
            handleMouseScroll(wheel);
        }
    } else {
        Serial.println("Unknown mouse action: " + action);
    }
}

void CommandParser::handleMouseButton(String action, String button) {
    button.toLowerCase();
    uint8_t mouseButton = 0;

    if (button == "left") {
        mouseButton = MOUSE_LEFT;
    } else if (button == "right") {
        mouseButton = MOUSE_RIGHT;
    } else if (button == "middle") {
        mouseButton = MOUSE_MIDDLE;
    } else if (button == "side1" || button == "back") {
        // Note: ESP32 USB HID library may not support all side buttons
        // This is a placeholder for extended functionality
        Serial.println("Side button 1 action: " + action);
        return;
    } else if (button == "side2" || button == "forward") {
        Serial.println("Side button 2 action: " + action);
        return;
    } else {
        Serial.println("Unknown mouse button: " + button);
        return;
    }

    if (action == "click") {
        mouse.click(mouseButton);
        Serial.println("Mouse clicked: " + button);
    } else if (action == "press") {
        mouse.press(mouseButton);
        Serial.println("Mouse pressed: " + button);
    } else if (action == "release") {
        mouse.release(mouseButton);
        Serial.println("Mouse released: " + button);
    }
}

void CommandParser::handleMouseMove(int x, int y) {
    mouse.move(x, y);
    Serial.println("Mouse moved: x=" + String(x) + ", y=" + String(y));
}

void CommandParser::handleMouseScroll(int wheel) {
    mouse.move(0, 0, wheel);
    Serial.println("Mouse scrolled: " + String(wheel));
}
