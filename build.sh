#!/bin/bash

echo "================================"
echo "ESP32-S3 USB HID 构建脚本"
echo "================================"

echo
echo "检查PlatformIO安装..."
if ! command -v pio &> /dev/null; then
    echo "错误: PlatformIO未安装或未添加到PATH"
    echo "请安装PlatformIO: https://platformio.org/install"
    exit 1
fi

echo "✓ PlatformIO已安装"

echo
echo "清理之前的构建..."
pio run --target clean

echo
echo "开始构建项目..."
pio run

if [ $? -ne 0 ]; then
    echo
    echo "❌ 构建失败!"
    echo "请检查代码和配置文件"
    exit 1
fi

echo
echo "✓ 构建成功!"
echo
echo "可用的操作:"
echo "1. 上传到ESP32-S3: pio run --target upload"
echo "2. 监控串口输出: pio device monitor"
echo "3. 运行测试服务器: python3 test_server.py"
echo

# 使脚本可执行
chmod +x build.sh
