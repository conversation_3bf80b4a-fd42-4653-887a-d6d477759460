# 快速开始指南

## 5分钟快速设置

### 1. 准备硬件
- ESP32-S3开发板
- USB-C数据线
- 目标计算机

### 2. 配置项目
编辑 `src/config.h`：
```cpp
#define WIFI_SSID "你的WiFi名称"
#define WIFI_PASSWORD "你的WiFi密码"
#define SERVER_PORT 8080  // ESP32-S3监听端口
```

### 3. 构建和上传
**Windows:**
```cmd
build.bat
pio run --target upload
```

**Linux/Mac:**
```bash
chmod +x build.sh
./build.sh
pio run --target upload
```

### 4. 测试连接
1. 连接ESP32-S3到目标计算机的USB端口

2. 通过串口监控查看ESP32-S3的IP地址：
```bash
pio device monitor
```

3. 使用客户端连接到ESP32-S3：
```bash
# 快速连接工具
connect_esp32.bat  # Windows
./connect_esp32.sh # Linux/Mac

# 或直接使用客户端
python esp32_client.py <ESP32_IP>
node esp32_client.js <ESP32_IP>
```

4. 输入测试命令：
```
key.a
mouse.click,left
```

## 常用命令示例

### 键盘
- `key.a` - 输入字母a
- `key.ctrl+c` - 复制
- `key.alt+tab` - 切换窗口
- `key.enter` - 回车

### 鼠标
- `mouse.click,left` - 左键点击
- `mouse.move,100,50` - 移动鼠标
- `mouse.scroll,0,0,3` - 向上滚动

## 故障排除

### ESP32-S3不被识别为HID设备
1. 检查USB线是否支持数据传输
2. 重新插拔USB连接
3. 检查设备管理器中的HID设备

### WiFi连接失败
1. 检查WiFi凭据
2. 确保ESP32-S3在WiFi范围内
3. 查看串口输出：`pio device monitor`

### 服务器连接失败
1. 检查IP地址是否正确
2. 确保防火墙允许端口8080
3. 验证ESP32-S3和电脑在同一网络

## 下一步

- 查看 `README.md` 了解详细功能
- 运行 `python test_commands.py <ESP32_IP> 8080` 进行自动化测试
- 根据需要修改命令格式和功能
