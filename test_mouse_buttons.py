#!/usr/bin/env python3
"""
鼠标按键测试脚本
专门测试ESP32-S3的鼠标按键功能，包括侧键
"""

import socket
import time
import sys

def test_mouse_buttons(esp32_ip, port=8080):
    """测试所有鼠标按键功能"""
    
    print(f"🖱️ 连接到ESP32-S3: {esp32_ip}:{port}")
    
    try:
        # 连接到ESP32-S3
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((esp32_ip, port))
        print("✓ 连接成功!")
        
        # 测试所有鼠标按键
        mouse_tests = [
            # 基本按键测试
            ("mouse.click,left", "左键点击"),
            ("mouse.click,right", "右键点击"),
            ("mouse.click,middle", "中键点击"),
            
            # 侧键测试
            ("mouse.click,side1", "侧键1点击 (后退)"),
            ("mouse.click,side2", "侧键2点击 (前进)"),
            ("mouse.click,back", "后退键点击"),
            ("mouse.click,forward", "前进键点击"),
            
            # 按下/释放测试
            ("mouse.press,left", "按下左键"),
            ("mouse.release,left", "释放左键"),
            ("mouse.press,side1", "按下侧键1"),
            ("mouse.release,side1", "释放侧键1"),
            
            # 移动和滚轮测试
            ("mouse.move,50,50", "鼠标移动"),
            ("mouse.scroll,0,0,3", "向上滚动"),
            ("mouse.scroll,0,0,-3", "向下滚动"),
        ]
        
        print("\n🧪 开始鼠标按键测试...")
        print("=" * 50)
        
        success_count = 0
        for i, (command, description) in enumerate(mouse_tests, 1):
            print(f"[{i:2d}/{len(mouse_tests)}] {description}")
            
            try:
                sock.send((command + '\n').encode())
                print(f"    ✓ 发送: {command}")
                success_count += 1
                time.sleep(1.5)  # 给用户时间观察效果
                
            except Exception as e:
                print(f"    ❌ 失败: {e}")
        
        print("=" * 50)
        print(f"📊 测试完成: {success_count}/{len(mouse_tests)} 成功")
        
        if success_count == len(mouse_tests):
            print("🎉 所有鼠标按键测试通过!")
        else:
            print("⚠️ 部分测试失败，请检查ESP32-S3连接")
            
    except ConnectionRefusedError:
        print(f"❌ 无法连接到 {esp32_ip}:{port}")
        print("请确保:")
        print("1. ESP32-S3已启动并连接WiFi")
        print("2. ESP32-S3正在运行服务器模式")
        print("3. IP地址正确")
        
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        
    finally:
        try:
            sock.close()
        except:
            pass

def interactive_mouse_test(esp32_ip, port=8080):
    """交互式鼠标测试"""
    
    print(f"🖱️ 交互式鼠标测试")
    print(f"连接到: {esp32_ip}:{port}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((esp32_ip, port))
        print("✓ 连接成功!")
        
        print("\n可用的鼠标命令:")
        print("1. left    - 左键点击")
        print("2. right   - 右键点击") 
        print("3. middle  - 中键点击")
        print("4. side1   - 侧键1点击 (后退)")
        print("5. side2   - 侧键2点击 (前进)")
        print("6. back    - 后退键点击")
        print("7. forward - 前进键点击")
        print("8. move    - 鼠标移动测试")
        print("9. scroll  - 滚轮测试")
        print("0. quit    - 退出")
        
        while True:
            print("\n" + "="*30)
            choice = input("选择测试 (0-9): ").strip()
            
            if choice == '0' or choice.lower() == 'quit':
                break
            elif choice == '1':
                send_command(sock, "mouse.click,left")
            elif choice == '2':
                send_command(sock, "mouse.click,right")
            elif choice == '3':
                send_command(sock, "mouse.click,middle")
            elif choice == '4':
                send_command(sock, "mouse.click,side1")
            elif choice == '5':
                send_command(sock, "mouse.click,side2")
            elif choice == '6':
                send_command(sock, "mouse.click,back")
            elif choice == '7':
                send_command(sock, "mouse.click,forward")
            elif choice == '8':
                print("测试鼠标移动...")
                send_command(sock, "mouse.move,100,0")
                time.sleep(0.5)
                send_command(sock, "mouse.move,-100,0")
            elif choice == '9':
                print("测试滚轮...")
                send_command(sock, "mouse.scroll,0,0,3")
                time.sleep(0.5)
                send_command(sock, "mouse.scroll,0,0,-3")
            else:
                print("无效选择")
                
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

def send_command(sock, command):
    """发送命令"""
    try:
        sock.send((command + '\n').encode())
        print(f"✓ 发送: {command}")
        time.sleep(0.5)
    except Exception as e:
        print(f"❌ 发送失败: {e}")

def main():
    if len(sys.argv) < 2:
        print("用法: python test_mouse_buttons.py <ESP32_IP> [port] [mode]")
        print("示例:")
        print("  python test_mouse_buttons.py ***************")
        print("  python test_mouse_buttons.py *************** 8080")
        print("  python test_mouse_buttons.py *************** 8080 interactive")
        print("")
        print("模式:")
        print("  auto (默认)    - 自动测试所有按键")
        print("  interactive   - 交互式测试")
        sys.exit(1)
    
    esp32_ip = sys.argv[1]
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 8080
    mode = sys.argv[3] if len(sys.argv) > 3 else "auto"
    
    print("🖱️ ESP32-S3 鼠标按键测试工具")
    print(f"目标: {esp32_ip}:{port}")
    print(f"模式: {mode}")
    
    if mode == "interactive":
        interactive_mouse_test(esp32_ip, port)
    else:
        test_mouse_buttons(esp32_ip, port)

if __name__ == "__main__":
    main()
