#!/bin/bash

echo "================================"
echo "ESP32-S3 HID 客户端连接工具"
echo "================================"

echo
echo "请确保ESP32-S3已经启动并连接到WiFi"
echo

read -p "请输入ESP32-S3的IP地址: " ESP32_IP

if [ -z "$ESP32_IP" ]; then
    echo "错误: 请输入有效的IP地址"
    exit 1
fi

echo
echo "选择客户端类型:"
echo "1. Python客户端 (交互模式)"
echo "2. Python客户端 (演示模式)"
echo "3. Node.js客户端 (交互模式)"
echo "4. Node.js客户端 (演示模式)"
echo "5. Node.js客户端 (测试模式)"
echo

read -p "请选择 (1-5): " CHOICE

case $CHOICE in
    1)
        echo "启动Python客户端 (交互模式)..."
        python3 esp32_client.py $ESP32_IP
        ;;
    2)
        echo "启动Python客户端 (演示模式)..."
        python3 esp32_client.py $ESP32_IP 8080 demo
        ;;
    3)
        echo "启动Node.js客户端 (交互模式)..."
        node esp32_client.js $ESP32_IP
        ;;
    4)
        echo "启动Node.js客户端 (演示模式)..."
        node esp32_client.js $ESP32_IP 8080 demo
        ;;
    5)
        echo "启动Node.js客户端 (测试模式)..."
        node esp32_client.js $ESP32_IP 8080 test
        ;;
    *)
        echo "无效选择"
        ;;
esac

echo
