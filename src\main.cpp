#include <Arduino.h>
#include <WiFi.h>
#include <WiFiClient.h>
#include "USB.h"
#include "USBHIDKeyboard.h"
#include "USBHIDMouse.h"
#include "CommandParser.h"
#include "NetworkManager.h"
#include "config.h"

// USB HID objects
USBHIDKeyboard keyboard;
USBHIDMouse mouse;

// Network manager
NetworkManager networkManager;
CommandParser commandParser;

void setup() {
    Serial.begin(SERIAL_BAUD_RATE);
    delay(1000);

    Serial.println("ESP32-S3 USB HID Keyboard/Mouse starting...");

    // Initialize USB HID
    keyboard.begin();
    mouse.begin();
    USB.begin();

    Serial.println("USB HID initialized");

    // Initialize command parser with HID objects
    commandParser.init(&keyboard, &mouse);

    // Initialize network manager
    networkManager.init(WIFI_SSID, WIFI_PASSWORD, SERVER_IP, SERVER_PORT);

    Serial.println("Setup complete");
}

void loop() {
    // Handle network connection and receive commands
    networkManager.handleConnection();

    // Process any received commands
    String command = networkManager.getReceivedCommand();
    if (command.length() > 0) {
        if (DEBUG_ENABLED) {
            Serial.println("Received command: " + command);
        }
        commandParser.parseAndExecute(command);
    }

    delay(COMMAND_DELAY);  // Small delay to prevent watchdog issues
}
