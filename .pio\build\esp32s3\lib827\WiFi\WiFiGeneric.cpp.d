.pio/build/esp32s3/lib827/WiFi/WiFiGeneric.cpp.o: \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiGeneric.cpp \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFi.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Print.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/WString.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/pgmspace.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Printable.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/IPAddress.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/WString.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Printable.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/IPv6Address.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiType.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_wifi_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_private/esp_wifi_types_private.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/esp_interface.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_event/include/esp_event_base.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiSTA.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiGeneric.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_event/include/esp_event.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/FreeRTOS.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/xtensa_config.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa-versions.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-isa.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-matmap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/tie.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/system.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/corebits.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime-frames.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_rom/include/esp_rom_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/reset_reasons.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_rom/include/esp32s3/rom/ets_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/soc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_assert.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_bit_defs.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/projdefs.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/portable.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/deprecated_definitions.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portmacro.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/specreg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime-core-state.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xt_instr_macros.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/soc/spinlock.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/soc/cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/esp_cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/include/hal/cpu_hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/soc_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/brownout_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/cpu_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/i2c_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/ledc_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/mpu_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/twai_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/include/hal/cpu_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/esp32s3/include/hal/cpu_ll.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/extreg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_attr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/soc/compare_set.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/include/soc/soc_memory_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_system/include/esp_private/crosscore_int.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_timer/include/esp_timer.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/newlib/platform_include/esp_newlib.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/heap/include/esp_heap_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/heap/include/multi_heap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_system/include/esp_system.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_idf_version.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/esp_mac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/esp_chip_info.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/esp_random.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portbenchmark.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_api.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portmacro_deprecated.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/mpu_wrappers.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/list.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/task_snapshot.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/queue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/semphr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/queue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_event/include/esp_event_base.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_event/include/esp_event_legacy.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_netif/include/esp_netif.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_ip_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_defaults.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_netif_glue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_eth/include/esp_eth.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_com.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/include/hal/eth_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_mac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_phy.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/tcpip_adapter/include/tcpip_adapter.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/tcpip_adapter/include/tcpip_adapter_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/opt.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/port/esp32/include/lwipopts.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/newlib/platform_include/sys/ioctl.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_system/include/esp_task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/port/esp32/include/netif/dhcp_state.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/port/esp32/include/sntp/sntp_get_set_time.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/debug.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/arch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/cc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/newlib/platform_include/errno.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/sys_arch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/vfs_lwip.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip4_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip6_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip6_zone.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/include/apps/dhcpserver/dhcpserver.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_sta_list.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_smartconfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/wifi_provisioning/include/wifi_provisioning/manager.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/protocomm/include/common/protocomm.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/protocomm/include/security/protocomm_security.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/wifi_provisioning/include/wifi_provisioning/wifi_config.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiAP.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiScan.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiClient.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Arduino.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp_arduino_version.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/esp_sleep.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/include/hal/touch_sensor_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/include/hal/gpio_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/include/soc/gpio_periph.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/io_mux_reg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_struct.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_reg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_sig_map.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/event_groups.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/freertos/include/freertos/timers.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/log/include/esp_log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/log/include/esp_log_internal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-matrix.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-uart.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-gpio.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/variants/esp32s3/pins_arduino.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-touch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-dac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/driver/include/driver/gpio.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/esp_intr_alloc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_common/include/esp_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_rom/include/esp32s3/rom/gpio.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-adc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-spi.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-i2c.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-ledc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-rmt.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-sigmadelta.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-timer.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-bt.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-psram.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-rgb-led.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal-cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp8266-compat.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/stdlib_noniso.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/binary.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/WCharacter.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Stream.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Print.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/IPAddress.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Client.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Server.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Udp.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Stream.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/HardwareSerial.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/HWCDC.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Esp.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/spi_flash/include/esp_partition.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/spi_flash/include/esp_flash.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/include/hal/spi_flash_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/hal/include/hal/esp_flash_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/spi_flash/include/esp_spi_flash.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/spi_flash/include/esp_spi_flash_counters.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32/spiram.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/io_pin_remap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Arduino.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Client.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiServer.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Server.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/libraries/WiFi/src/WiFiUdp.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/Udp.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/cbuf.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_wifi.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_wifi_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_private/esp_wifi_private.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_wifi_crypto_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_private/wifi_os_adapter.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/esp_wifi/include/esp_wifi_default.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/dns.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/tools/sdk/esp32s3/include/lwip/include/apps/dhcpserver/dhcpserver_options.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32@3.20014.231204/cores/esp32/esp32-hal.h
