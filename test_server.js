#!/usr/bin/env node

/**
 * ESP32-S3 USB HID 测试服务器 (Node.js版本)
 * 提供Socket服务器和Web界面来控制ESP32-S3
 */

const net = require('net');
const http = require('http');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

class HIDTestServer {
    constructor(socketPort = 8080, webPort = 3000) {
        this.socketPort = socketPort;
        this.webPort = webPort;
        this.clients = new Set();
        this.server = null;
        this.webServer = null;
        this.rl = null;
    }

    start() {
        this.startSocketServer();
        this.startWebServer();
        this.startCommandInterface();
    }

    startSocketServer() {
        this.server = net.createServer((socket) => {
            const clientInfo = `${socket.remoteAddress}:${socket.remotePort}`;
            console.log(`✓ ESP32-S3 连接: ${clientInfo}`);
            
            this.clients.add(socket);
            
            socket.on('data', (data) => {
                console.log(`收到来自 ${clientInfo}: ${data.toString().trim()}`);
            });
            
            socket.on('close', () => {
                console.log(`✗ ESP32-S3 断开: ${clientInfo}`);
                this.clients.delete(socket);
            });
            
            socket.on('error', (err) => {
                console.log(`连接错误 ${clientInfo}: ${err.message}`);
                this.clients.delete(socket);
            });
        });

        this.server.listen(this.socketPort, () => {
            console.log(`🚀 Socket服务器启动: 端口 ${this.socketPort}`);
            console.log(`等待ESP32-S3连接...`);
        });
    }

    startWebServer() {
        this.webServer = http.createServer((req, res) => {
            if (req.method === 'GET' && req.url === '/') {
                this.serveWebInterface(res);
            } else if (req.method === 'POST' && req.url === '/command') {
                this.handleWebCommand(req, res);
            } else if (req.method === 'GET' && req.url === '/status') {
                this.handleStatusRequest(res);
            } else {
                res.writeHead(404);
                res.end('Not Found');
            }
        });

        this.webServer.listen(this.webPort, () => {
            console.log(`🌐 Web界面启动: http://localhost:${this.webPort}`);
        });
    }

    serveWebInterface(res) {
        const html = this.generateWebInterface();
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(html);
    }

    generateWebInterface() {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32-S3 USB HID 控制器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; border-radius: 4px; margin-bottom: 20px; }
        .status.connected { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.disconnected { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .section { margin-bottom: 30px; }
        .section h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .button-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin: 10px 0; }
        button { padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        button:hover { opacity: 0.8; }
        input[type="text"], input[type="number"] { padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin: 5px; }
        .custom-command { display: flex; gap: 10px; align-items: center; margin: 10px 0; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 ESP32-S3 USB HID 控制器</h1>
        <div id="status" class="status disconnected">状态: 未连接</div>
        
        <div class="section">
            <h3>⌨️ 键盘控制</h3>
            <div class="button-grid">
                <button class="btn-primary" onclick="sendCommand('key.a')">字母 A</button>
                <button class="btn-primary" onclick="sendCommand('key.enter')">回车</button>
                <button class="btn-primary" onclick="sendCommand('key.space')">空格</button>
                <button class="btn-primary" onclick="sendCommand('key.backspace')">退格</button>
                <button class="btn-warning" onclick="sendCommand('key.ctrl+c')">Ctrl+C</button>
                <button class="btn-warning" onclick="sendCommand('key.ctrl+v')">Ctrl+V</button>
                <button class="btn-warning" onclick="sendCommand('key.alt+tab')">Alt+Tab</button>
                <button class="btn-warning" onclick="sendCommand('key.ctrl+shift+esc')">任务管理器</button>
            </div>
            <div class="custom-command">
                <input type="text" id="keyCommand" placeholder="输入键盘命令，如: key.ctrl+a" style="flex: 1;">
                <button class="btn-success" onclick="sendCustomKeyCommand()">发送</button>
            </div>
        </div>

        <div class="section">
            <h3>🖱️ 鼠标控制</h3>
            <div class="button-grid">
                <button class="btn-primary" onclick="sendCommand('mouse.click,left')">左键点击</button>
                <button class="btn-primary" onclick="sendCommand('mouse.click,right')">右键点击</button>
                <button class="btn-primary" onclick="sendCommand('mouse.click,middle')">中键点击</button>
                <button class="btn-success" onclick="sendCommand('mouse.scroll,0,0,3')">向上滚动</button>
                <button class="btn-success" onclick="sendCommand('mouse.scroll,0,0,-3')">向下滚动</button>
            </div>
            <div class="custom-command">
                <label>移动:</label>
                <input type="number" id="mouseX" placeholder="X" value="100" style="width: 60px;">
                <input type="number" id="mouseY" placeholder="Y" value="50" style="width: 60px;">
                <button class="btn-success" onclick="sendMouseMove()">移动鼠标</button>
            </div>
            <div class="custom-command">
                <input type="text" id="mouseCommand" placeholder="输入鼠标命令，如: mouse.click,left" style="flex: 1;">
                <button class="btn-success" onclick="sendCustomMouseCommand()">发送</button>
            </div>
        </div>

        <div class="section">
            <h3>🧪 测试序列</h3>
            <div class="button-grid">
                <button class="btn-warning" onclick="runKeyboardTest()">键盘测试</button>
                <button class="btn-warning" onclick="runMouseTest()">鼠标测试</button>
                <button class="btn-danger" onclick="runFullTest()">完整测试</button>
            </div>
        </div>

        <div class="section">
            <h3>📝 命令日志</h3>
            <div id="log" class="log"></div>
            <button class="btn-primary" onclick="clearLog()" style="margin-top: 10px;">清空日志</button>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        
        function updateStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('status');
                    if (data.connected) {
                        statusElement.className = 'status connected';
                        statusElement.textContent = \`状态: 已连接 (\${data.clientCount} 个设备)\`;
                    } else {
                        statusElement.className = 'status disconnected';
                        statusElement.textContent = '状态: 未连接';
                    }
                })
                .catch(() => {
                    const statusElement = document.getElementById('status');
                    statusElement.className = 'status disconnected';
                    statusElement.textContent = '状态: 连接错误';
                });
        }
        
        function sendCommand(command) {
            fetch('/command', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ command: command })
            })
            .then(response => response.json())
            .then(data => {
                addLog(\`发送: \${command}\`);
                if (!data.success) {
                    addLog(\`错误: \${data.error}\`, 'error');
                }
            })
            .catch(error => {
                addLog(\`发送失败: \${error.message}\`, 'error');
            });
        }
        
        function sendCustomKeyCommand() {
            const command = document.getElementById('keyCommand').value.trim();
            if (command) {
                sendCommand(command);
                document.getElementById('keyCommand').value = '';
            }
        }
        
        function sendCustomMouseCommand() {
            const command = document.getElementById('mouseCommand').value.trim();
            if (command) {
                sendCommand(command);
                document.getElementById('mouseCommand').value = '';
            }
        }
        
        function sendMouseMove() {
            const x = document.getElementById('mouseX').value;
            const y = document.getElementById('mouseY').value;
            sendCommand(\`mouse.move,\${x},\${y}\`);
        }
        
        function runKeyboardTest() {
            const tests = ['key.a', 'key.space', 'key.enter', 'key.ctrl+c', 'key.alt+tab'];
            runTestSequence(tests, '键盘测试');
        }
        
        function runMouseTest() {
            const tests = ['mouse.click,left', 'mouse.move,100,50', 'mouse.scroll,0,0,3', 'mouse.click,right'];
            runTestSequence(tests, '鼠标测试');
        }
        
        function runFullTest() {
            const tests = [
                'key.a', 'key.ctrl+a', 'key.ctrl+c', 
                'mouse.click,left', 'mouse.move,100,50', 'mouse.scroll,0,0,3'
            ];
            runTestSequence(tests, '完整测试');
        }
        
        function runTestSequence(commands, testName) {
            addLog(\`开始 \${testName}...\`, 'info');
            commands.forEach((cmd, index) => {
                setTimeout(() => {
                    sendCommand(cmd);
                    if (index === commands.length - 1) {
                        addLog(\`\${testName} 完成\`, 'success');
                    }
                }, index * 1000);
            });
        }
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'color: red' : type === 'success' ? 'color: green' : type === 'info' ? 'color: blue' : '';
            logElement.innerHTML += \`<div style="\${className}">[\${timestamp}] \${message}</div>\`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            logElement.innerHTML = '';
        }
        
        // 定期更新状态
        updateStatus();
        setInterval(updateStatus, 2000);
    </script>
</body>
</html>`;
    }

    handleWebCommand(req, res) {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const { command } = JSON.parse(body);
                const success = this.sendCommand(command);
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success, command }));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: error.message }));
            }
        });
    }

    handleStatusRequest(res) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            connected: this.clients.size > 0,
            clientCount: this.clients.size
        }));
    }

    startCommandInterface() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        console.log('\n=== ESP32-S3 HID 远程控制 ===');
        console.log('命令示例:');
        console.log('  键盘: key.a, key.ctrl+c, key.alt+tab');
        console.log('  鼠标: mouse.click,left, mouse.move,100,50');
        console.log('  输入 "help" 查看更多示例');
        console.log('  输入 "quit" 退出程序');
        console.log('===============================\n');

        this.promptCommand();
    }

    promptCommand() {
        this.rl.question('输入命令: ', (command) => {
            command = command.trim();
            
            if (command.toLowerCase() === 'quit') {
                this.shutdown();
                return;
            }
            
            if (command.toLowerCase() === 'help') {
                this.showHelp();
            } else if (command) {
                this.sendCommand(command);
            }
            
            this.promptCommand();
        });
    }

    sendCommand(command) {
        if (this.clients.size === 0) {
            console.log('❌ 没有连接的ESP32-S3设备');
            return false;
        }

        let success = true;
        const disconnectedClients = [];
        
        for (const client of this.clients) {
            try {
                client.write(command + '\n');
                console.log(`✓ 发送: ${command}`);
            } catch (error) {
                console.log(`❌ 发送失败: ${error.message}`);
                disconnectedClients.push(client);
                success = false;
            }
        }

        // 清理断开的连接
        disconnectedClients.forEach(client => {
            this.clients.delete(client);
        });

        return success;
    }

    showHelp() {
        console.log('\n=== 命令帮助 ===');
        console.log('键盘命令:');
        console.log('  key.a                 - 按下字母a');
        console.log('  key.ctrl+c            - Ctrl+C组合键');
        console.log('  key.alt+tab           - Alt+Tab组合键');
        console.log('  key.enter             - 回车键');
        console.log('  key.space             - 空格键');
        console.log('');
        console.log('鼠标命令:');
        console.log('  mouse.click,left      - 左键点击');
        console.log('  mouse.move,100,50     - 移动鼠标');
        console.log('  mouse.scroll,0,0,3    - 向上滚动');
        console.log('================\n');
    }

    shutdown() {
        console.log('\n正在关闭服务器...');
        
        if (this.rl) {
            this.rl.close();
        }
        
        if (this.server) {
            this.server.close();
        }
        
        if (this.webServer) {
            this.webServer.close();
        }
        
        process.exit(0);
    }
}

// 启动服务器
const server = new HIDTestServer();
server.start();

// 处理程序退出
process.on('SIGINT', () => {
    server.shutdown();
});

process.on('SIGTERM', () => {
    server.shutdown();
});
