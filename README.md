# ESP32-S3 USB HID 键盘鼠标远程控制

这个项目使用ESP32-S3开发板实现USB HID键盘和鼠标功能，支持通过WiFi接收Socket命令进行远程控制。

## 功能特性

- ✅ USB HID键盘模拟（支持组合键）
- ✅ USB HID鼠标模拟（支持移动、点击、滚轮）
- ✅ WiFi连接和Socket通信
- ✅ 命令解析器支持多种格式
- ✅ 自动重连机制

## 硬件要求

- ESP32-S3开发板（支持USB OTG功能）
- USB-C数据线
- 计算机（作为USB HID设备的目标）

## 软件要求

- PlatformIO IDE
- Python 3.x（用于测试服务器）

## 安装和配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd usbhid
```

### 2. 配置WiFi和服务器设置
编辑 `src/config.h` 文件：
```cpp
#define WIFI_SSID "你的WiFi名称"
#define WIFI_PASSWORD "你的WiFi密码"
#define SERVER_IP "*************"  // 你的服务器IP地址
#define SERVER_PORT 8080
```

### 3. 编译和上传
```bash
pio run --target upload
```

### 4. 启动测试服务器
```bash
python test_server.py
```

## 命令格式

### 键盘命令
格式：`key.<按键>`

**单键示例：**
- `key.a` - 按下字母a
- `key.enter` - 按下回车键
- `key.space` - 按下空格键
- `key.f1` - 按下F1功能键
- `key.up` - 按下上箭头键

**组合键示例：**
- `key.ctrl+c` - Ctrl+C组合键
- `key.alt+tab` - Alt+Tab组合键
- `key.shift+a` - Shift+A（大写A）
- `key.ctrl+shift+esc` - Ctrl+Shift+Esc

**支持的特殊键：**
- 修饰键：`ctrl`, `shift`, `alt`, `win`
- 箭头键：`up`, `down`, `left`, `right`
- 功能键：`f1`-`f12`
- 其他：`enter`, `space`, `backspace`, `delete`, `tab`, `esc`

### 鼠标命令
格式：`mouse.<动作>,<参数>`

**点击命令：**
- `mouse.click,left` - 左键单击
- `mouse.click,right` - 右键单击
- `mouse.click,middle` - 中键单击

**按下/释放命令：**
- `mouse.press,left` - 按下左键（不释放）
- `mouse.release,left` - 释放左键

**移动命令：**
- `mouse.move,100,50` - 鼠标相对移动（x=100, y=50）
- `mouse.move,-50,30` - 支持负值移动

**滚轮命令：**
- `mouse.scroll,0,0,3` - 向上滚动3个单位
- `mouse.scroll,0,0,-2` - 向下滚动2个单位

## 使用方法

### 1. 连接硬件
1. 将ESP32-S3通过USB-C线连接到目标计算机
2. ESP32-S3会被识别为USB HID键盘和鼠标设备

### 2. 启动设备
1. 给ESP32-S3上电
2. 设备会自动连接到配置的WiFi网络
3. 连接到指定的Socket服务器

### 3. 发送命令
1. 运行测试服务器：`python test_server.py`
2. 在服务器控制台输入命令
3. 命令会通过WiFi发送到ESP32-S3
4. ESP32-S3执行相应的键盘/鼠标操作

### 示例会话
```
Enter command: key.a
Sent: key.a

Enter command: mouse.click,left
Sent: mouse.click,left

Enter command: key.ctrl+c
Sent: key.ctrl+c

Enter command: mouse.move,100,50
Sent: mouse.move,100,50
```

## 故障排除

### 1. USB HID不工作
- 确保使用支持USB OTG的ESP32-S3开发板
- 检查USB线是否支持数据传输
- 重新插拔USB连接

### 2. WiFi连接失败
- 检查WiFi凭据是否正确
- 确保WiFi网络可用
- 查看串口输出的错误信息

### 3. Socket连接失败
- 检查服务器IP地址和端口
- 确保防火墙允许连接
- 验证服务器是否正在运行

### 4. 命令不响应
- 检查命令格式是否正确
- 查看串口输出的调试信息
- 确保设备已连接到服务器

## 开发说明

### 项目结构
```
├── src/
│   ├── main.cpp           # 主程序
│   ├── CommandParser.h    # 命令解析器头文件
│   ├── CommandParser.cpp  # 命令解析器实现
│   ├── NetworkManager.h   # 网络管理器头文件
│   ├── NetworkManager.cpp # 网络管理器实现
│   └── config.h          # 配置文件
├── test_server.py        # 测试服务器
├── platformio.ini        # PlatformIO配置
└── README.md            # 说明文档
```

### 扩展功能
- 可以添加更多的鼠标侧键支持
- 可以实现宏命令功能
- 可以添加Web界面控制
- 可以支持多设备连接

## 许可证

MIT License
