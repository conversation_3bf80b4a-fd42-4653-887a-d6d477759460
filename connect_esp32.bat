@echo off
echo ================================
echo ESP32-S3 HID 客户端连接工具
echo ================================

echo.
echo 请确保ESP32-S3已经启动并连接到WiFi
echo.

set /p ESP32_IP="请输入ESP32-S3的IP地址: "

if "%ESP32_IP%"=="" (
    echo 错误: 请输入有效的IP地址
    pause
    exit /b 1
)

echo.
echo 选择客户端类型:
echo 1. Python客户端 (交互模式)
echo 2. Python客户端 (演示模式)
echo 3. Node.js客户端 (交互模式)
echo 4. Node.js客户端 (演示模式)
echo 5. Node.js客户端 (测试模式)
echo.

set /p CHOICE="请选择 (1-5): "

if "%CHOICE%"=="1" (
    echo 启动Python客户端 (交互模式)...
    python esp32_client.py %ESP32_IP%
) else if "%CHOICE%"=="2" (
    echo 启动Python客户端 (演示模式)...
    python esp32_client.py %ESP32_IP% 8080 demo
) else if "%CHOICE%"=="3" (
    echo 启动Node.js客户端 (交互模式)...
    node esp32_client.js %ESP32_IP%
) else if "%CHOICE%"=="4" (
    echo 启动Node.js客户端 (演示模式)...
    node esp32_client.js %ESP32_IP% 8080 demo
) else if "%CHOICE%"=="5" (
    echo 启动Node.js客户端 (测试模式)...
    node esp32_client.js %ESP32_IP% 8080 test
) else (
    echo 无效选择
)

echo.
pause
