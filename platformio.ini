[env:esp32s3]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; USB HID configuration
board_build.arduino.usb_mode = 1
board_build.arduino.usb_cdc_on_boot = 0

; Build flags for USB HID
build_flags = 
    -DARDUINO_USB_MODE=1
    -<PERSON>ARDUINO_USB_CDC_ON_BOOT=0
    -DCORE_DEBUG_LEVEL=0

; Libraries
lib_deps =
    ; No external libraries needed for USB HID on ESP32-S3
    ; USB HID functionality is built into the ESP32-S3 Arduino core

; Monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Upload settings
upload_speed = 921600
