@echo off
echo ================================
echo ESP32-S3 HID Node.js 测试服务器
echo ================================

echo.
echo 检查Node.js安装...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Node.js未安装
    echo 请从 https://nodejs.org 下载并安装Node.js
    pause
    exit /b 1
)

echo ✓ Node.js已安装
node --version

echo.
echo 启动测试服务器...
echo.
echo 服务器信息:
echo - Socket服务器: 端口 8080 (ESP32-S3连接)
echo - Web界面: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务器
echo ================================

node test_server.js

pause
