{"name": "esp32s3-hid-controller", "version": "1.0.0", "description": "ESP32-S3 USB HID 键盘鼠标远程控制测试服务器", "main": "test_server.js", "scripts": {"start": "node test_server.js", "test": "node test_client.js", "dev": "node test_server.js"}, "keywords": ["esp32", "esp32s3", "usb", "hid", "keyboard", "mouse", "remote", "control"], "author": "ESP32-S3 HID Project", "license": "MIT", "engines": {"node": ">=12.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "."}, "bugs": {"url": "."}, "homepage": "."}