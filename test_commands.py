#!/usr/bin/env python3
"""
自动化测试脚本 - 发送一系列测试命令到ESP32-S3
"""

import socket
import time
import sys

def send_command(sock, command):
    """发送命令到ESP32-S3"""
    try:
        sock.send((command + '\n').encode())
        print(f"✓ 发送: {command}")
        time.sleep(1)  # 等待命令执行
        return True
    except Exception as e:
        print(f"✗ 发送失败: {command} - {e}")
        return False

def test_keyboard_commands(sock):
    """测试键盘命令"""
    print("\n=== 测试键盘功能 ===")
    
    keyboard_tests = [
        "key.a",           # 单个字母
        "key.space",       # 空格键
        "key.enter",       # 回车键
        "key.backspace",   # 退格键
        "key.tab",         # Tab键
        "key.esc",         # Esc键
        "key.f1",          # 功能键
        "key.up",          # 箭头键
        "key.ctrl+c",      # 组合键
        "key.alt+tab",     # Alt+Tab
        "key.shift+a",     # Shift+A
        "key.ctrl+shift+esc"  # 三键组合
    ]
    
    success_count = 0
    for cmd in keyboard_tests:
        if send_command(sock, cmd):
            success_count += 1
    
    print(f"键盘测试完成: {success_count}/{len(keyboard_tests)} 成功")
    return success_count == len(keyboard_tests)

def test_mouse_commands(sock):
    """测试鼠标命令"""
    print("\n=== 测试鼠标功能 ===")
    
    mouse_tests = [
        "mouse.click,left",      # 左键点击
        "mouse.click,right",     # 右键点击
        "mouse.click,middle",    # 中键点击
        "mouse.press,left",      # 按下左键
        "mouse.release,left",    # 释放左键
        "mouse.move,100,50",     # 鼠标移动
        "mouse.move,-50,30",     # 负值移动
        "mouse.scroll,0,0,3",    # 向上滚动
        "mouse.scroll,0,0,-2",   # 向下滚动
    ]
    
    success_count = 0
    for cmd in mouse_tests:
        if send_command(sock, cmd):
            success_count += 1
    
    print(f"鼠标测试完成: {success_count}/{len(mouse_tests)} 成功")
    return success_count == len(mouse_tests)

def interactive_demo(sock):
    """交互式演示"""
    print("\n=== 交互式演示 ===")
    print("将执行一个简单的演示序列...")
    
    demo_sequence = [
        ("key.ctrl+a", "全选文本"),
        ("key.ctrl+c", "复制文本"),
        ("key.right", "移动光标"),
        ("key.ctrl+v", "粘贴文本"),
        ("mouse.move,100,0", "鼠标右移"),
        ("mouse.click,left", "左键点击"),
        ("mouse.scroll,0,0,3", "向上滚动"),
    ]
    
    for cmd, desc in demo_sequence:
        print(f"执行: {desc}")
        send_command(sock, cmd)
        time.sleep(1.5)
    
    print("演示完成!")

def main():
    if len(sys.argv) != 3:
        print("用法: python test_commands.py <ESP32_IP> <PORT>")
        print("示例: python test_commands.py ************* 8080")
        sys.exit(1)
    
    esp32_ip = sys.argv[1]
    port = int(sys.argv[2])
    
    print(f"连接到 ESP32-S3: {esp32_ip}:{port}")
    
    try:
        # 连接到ESP32-S3（作为客户端）
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((esp32_ip, port))
        print("✓ 连接成功!")
        
        # 运行测试
        keyboard_ok = test_keyboard_commands(sock)
        mouse_ok = test_mouse_commands(sock)
        
        if keyboard_ok and mouse_ok:
            print("\n🎉 所有测试通过!")
            
            # 询问是否运行演示
            response = input("\n是否运行交互式演示? (y/n): ")
            if response.lower() == 'y':
                interactive_demo(sock)
        else:
            print("\n❌ 部分测试失败，请检查ESP32-S3连接和配置")
        
    except ConnectionRefusedError:
        print(f"❌ 无法连接到 {esp32_ip}:{port}")
        print("请确保:")
        print("1. ESP32-S3已连接到WiFi")
        print("2. ESP32-S3正在运行服务器模式")
        print("3. IP地址和端口正确")
    except Exception as e:
        print(f"❌ 连接错误: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    main()
