# Node.js 测试服务器使用指南

## 功能特性

Node.js版本的测试服务器提供了比Python版本更丰富的功能：

- ✅ **Socket服务器** - 与ESP32-S3通信
- ✅ **Web图形界面** - 浏览器控制面板
- ✅ **命令行界面** - 终端交互控制
- ✅ **自动化测试客户端** - 批量测试功能
- ✅ **实时状态监控** - 连接状态显示
- ✅ **命令日志** - 操作历史记录

## 快速开始

### 1. 启动服务器

**Windows:**
```cmd
start_nodejs_server.bat
```

**Linux/Mac:**
```bash
chmod +x start_nodejs_server.sh
./start_nodejs_server.sh
```

**手动启动:**
```bash
node test_server.js
```

### 2. 访问Web界面

启动服务器后，打开浏览器访问：
```
http://localhost:3000
```

## Web界面功能

### 连接状态
- 🟢 **已连接** - ESP32-S3设备已连接
- 🔴 **未连接** - 等待ESP32-S3连接

### 键盘控制区域
- **快捷按钮** - 常用按键和组合键
- **自定义命令** - 输入任意键盘命令

### 鼠标控制区域
- **点击按钮** - 左键、右键、中键点击
- **滚轮控制** - 向上/向下滚动
- **移动控制** - 自定义X、Y坐标移动
- **自定义命令** - 输入任意鼠标命令

### 测试序列
- **键盘测试** - 自动测试所有键盘功能
- **鼠标测试** - 自动测试所有鼠标功能
- **完整测试** - 综合测试所有功能

### 命令日志
- 实时显示发送的命令
- 显示时间戳
- 错误信息高亮显示
- 支持清空日志

## 命令行界面

启动服务器后，可以在终端直接输入命令：

```
输入命令: key.a
✓ 发送: key.a

输入命令: mouse.click,left
✓ 发送: mouse.click,left

输入命令: help
=== 命令帮助 ===
...

输入命令: quit
正在关闭服务器...
```

## 自动化测试客户端

### 基本用法

```bash
# 连接到本地服务器，运行完整测试
node test_client.js

# 连接到远程服务器
node test_client.js *************

# 指定端口
node test_client.js localhost 8080

# 指定测试模式
node test_client.js localhost 8080 2
```

### 测试模式

1. **完整测试** - 键盘 + 鼠标功能测试
2. **仅键盘测试** - 只测试键盘功能
3. **仅鼠标测试** - 只测试鼠标功能
4. **交互式演示** - 模拟真实使用场景
5. **压力测试** - 快速发送大量命令

### 示例输出

```
🚀 ESP32-S3 HID 自动化测试客户端
连接目标: localhost:8080
✓ 连接到服务器: localhost:8080

🧪 运行完整测试...

🎹 开始键盘功能测试...
测试: 单个字母按键
📤 发送命令: key.a
测试: 空格键
📤 发送命令: key.space
...

键盘测试完成: 12/12 成功

🖱️ 开始鼠标功能测试...
测试: 左键点击
📤 发送命令: mouse.click,left
...

鼠标测试完成: 9/9 成功

📊 测试结果:
🎉 所有测试通过!
```

## 服务器配置

### 端口设置

默认端口：
- **Socket服务器**: 8080 (ESP32-S3连接)
- **Web服务器**: 3000 (浏览器访问)

修改端口：
```javascript
// 在 test_server.js 中修改
const server = new HIDTestServer(8080, 3000);
//                              ^^^^  ^^^^
//                            Socket Web
```

### 多设备支持

服务器支持多个ESP32-S3设备同时连接：
- 每个设备独立连接
- 命令会广播到所有连接的设备
- Web界面显示连接设备数量

## 故障排除

### 服务器启动失败

**端口被占用:**
```
Error: listen EADDRINUSE :::8080
```
解决方案：
1. 关闭占用端口的程序
2. 修改服务器端口设置

**Node.js未安装:**
```
'node' 不是内部或外部命令
```
解决方案：
1. 从 https://nodejs.org 下载安装Node.js
2. 重启命令行工具

### Web界面无法访问

1. 确认服务器已启动
2. 检查防火墙设置
3. 尝试使用 `127.0.0.1:3000` 而不是 `localhost:3000`

### ESP32-S3连接失败

1. 检查ESP32-S3的WiFi连接
2. 确认服务器IP地址配置正确
3. 检查防火墙是否阻止8080端口

## 开发和扩展

### 添加新命令

在 `test_server.js` 中的Web界面部分添加新按钮：

```javascript
<button class="btn-primary" onclick="sendCommand('key.新命令')">新功能</button>
```

### 自定义测试序列

在 `test_client.js` 中添加新的测试序列：

```javascript
async runCustomTest() {
    const customTests = [
        { cmd: 'key.custom1', desc: '自定义测试1' },
        { cmd: 'mouse.custom2', desc: '自定义测试2' }
    ];
    // ... 测试逻辑
}
```

### API扩展

服务器提供简单的HTTP API：

- `GET /` - Web界面
- `POST /command` - 发送命令
- `GET /status` - 获取连接状态

可以扩展更多API端点来支持高级功能。

## 性能优化

### 命令发送频率

建议命令间隔：
- **普通使用**: 100-500ms
- **快速操作**: 50-100ms
- **压力测试**: 10-50ms

### 内存使用

服务器会自动清理断开的连接，避免内存泄漏。

### 网络优化

- 使用TCP长连接减少连接开销
- 命令格式简洁，减少网络传输量
- 支持批量命令发送（可扩展功能）

## 总结

Node.js版本的测试服务器提供了完整的ESP32-S3 HID控制解决方案，包括：

1. **易用的Web界面** - 适合日常使用和演示
2. **强大的命令行工具** - 适合开发和调试
3. **自动化测试框架** - 适合质量保证和批量测试
4. **灵活的扩展性** - 支持自定义功能开发

选择适合你需求的使用方式，享受远程控制ESP32-S3的便利！
