#ifndef COMMAND_PARSER_H
#define COMMAND_PARSER_H

#include <Arduino.h>
#include "USBHIDKeyboard.h"
#include "USBHIDMouse.h"

// Key codes for special keys
#define KEY_CTRL_LEFT    0x80
#define KEY_SHIFT_LEFT   0x81
#define KEY_ALT_LEFT     0x82
#define KEY_GUI_LEFT     0x83
#define KEY_CTRL_RIGHT   0x84
#define KEY_SHIFT_RIGHT  0x85
#define KEY_ALT_RIGHT    0x86
#define KEY_GUI_RIGHT    0x87
#define KEY_UP_ARROW     0xDA
#define KEY_DOWN_ARROW   0xD9
#define KEY_LEFT_ARROW   0xD8
#define KEY_RIGHT_ARROW  0xD7
#define KEY_BACKSPACE    0xB2
#define KEY_TAB          0xB3
#define KEY_RETURN       0xB0
#define KEY_ESC          0xB1
#define KEY_INSERT       0xD1
#define KEY_DELETE       0xD4
#define KEY_PAGE_UP      0xD3
#define KEY_PAGE_DOWN    0xD6
#define KEY_HOME         0xD2
#define KEY_END          0xD5
#define KEY_CAPS_LOCK    0xC1
#define KEY_F1           0xC2
#define KEY_F2           0xC3
#define KEY_F3           0xC4
#define KEY_F4           0xC5
#define KEY_F5           0xC6
#define KEY_F6           0xC7
#define KEY_F7           0xC8
#define KEY_F8           0xC9
#define KEY_F9           0xCA
#define KEY_F10          0xCB
#define KEY_F11          0xCC
#define KEY_F12          0xCD

class CommandParser {
private:
    USBHIDKeyboard* keyboard;
    USBHIDMouse* mouse;

    // Mouse button states
    bool leftButtonPressed = false;
    bool rightButtonPressed = false;
    bool middleButtonPressed = false;
    bool sideButton1Pressed = false;
    bool sideButton2Pressed = false;
    bool sideButton3Pressed = false;
    bool sideButton4Pressed = false;
    bool sideButton5Pressed = false;

    // Helper methods
    void parseKeyboardCommand(String command);
    void parseMouseCommand(String command);
    uint8_t getKeyCode(String key);
    uint8_t getModifierKey(String key);
    void handleMouseButton(String action, String button);
    void handleMouseMove(int x, int y);
    void handleMouseScroll(int wheel);
    void sendKeyPress(String key);
    void sendKeyCombo(String combo);

public:
    void init(USBHIDKeyboard* kb, USBHIDMouse* ms);
    void parseAndExecute(String command);
};

#endif
