@echo off
echo ================================
echo ESP32-S3 USB HID 构建脚本
echo ================================

echo.
echo 检查PlatformIO安装...
pio --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: PlatformIO未安装或未添加到PATH
    echo 请安装PlatformIO: https://platformio.org/install
    pause
    exit /b 1
)

echo ✓ PlatformIO已安装

echo.
echo 清理之前的构建...
pio run --target clean

echo.
echo 开始构建项目...
pio run

if %errorlevel% neq 0 (
    echo.
    echo ❌ 构建失败!
    echo 请检查代码和配置文件
    pause
    exit /b 1
)

echo.
echo ✓ 构建成功!
echo.
echo 可用的操作:
echo 1. 上传到ESP32-S3: pio run --target upload
echo 2. 监控串口输出: pio device monitor
echo 3. 运行测试服务器: python test_server.py
echo.

pause
