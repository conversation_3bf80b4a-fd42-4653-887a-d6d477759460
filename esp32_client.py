#!/usr/bin/env python3
"""
ESP32-S3 Socket客户端
连接到ESP32-S3服务器发送HID命令
"""

import socket
import time
import sys

class ESP32Client:
    def __init__(self, esp32_ip, port=8080):
        self.esp32_ip = esp32_ip
        self.port = port
        self.socket = None
        self.connected = False
        
    def connect(self):
        """连接到ESP32-S3服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.esp32_ip, self.port))
            self.connected = True
            print(f"✓ 已连接到ESP32-S3: {self.esp32_ip}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def send_command(self, command):
        """发送命令到ESP32-S3"""
        if not self.connected:
            print("❌ 未连接到ESP32-S3")
            return False
            
        try:
            self.socket.send((command + '\n').encode())
            print(f"📤 发送: {command}")
            return True
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            self.socket.close()
            self.connected = False
            print("✗ 已断开连接")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== ESP32-S3 HID 控制器 ===")
        print("命令示例:")
        print("  键盘: key.a, key.ctrl+c, key.alt+tab")
        print("  鼠标: mouse.click,left, mouse.move,100,50")
        print("  输入 'help' 查看帮助, 'quit' 退出")
        print("========================\n")
        
        while self.connected:
            try:
                command = input("输入命令: ").strip()
                
                if command.lower() == 'quit':
                    break
                elif command.lower() == 'help':
                    self.show_help()
                elif command:
                    self.send_command(command)
                    
            except KeyboardInterrupt:
                break
                
        self.disconnect()
    
    def show_help(self):
        """显示帮助信息"""
        print("\n=== 命令帮助 ===")
        print("键盘命令:")
        print("  key.a                 - 按下字母a")
        print("  key.ctrl+c            - Ctrl+C组合键")
        print("  key.alt+tab           - Alt+Tab组合键")
        print("  key.enter             - 回车键")
        print("  key.space             - 空格键")
        print("  key.f1                - 功能键F1")
        print("  key.up                - 上箭头键")
        print("")
        print("鼠标命令:")
        print("  mouse.click,left      - 左键点击")
        print("  mouse.click,right     - 右键点击")
        print("  mouse.click,middle    - 中键点击")
        print("  mouse.click,side1     - 侧键1点击 (后退)")
        print("  mouse.click,side2     - 侧键2点击 (前进)")
        print("  mouse.click,back      - 后退键点击 (同side1)")
        print("  mouse.click,forward   - 前进键点击 (同side2)")
        print("  mouse.press,left      - 按下左键")
        print("  mouse.release,left    - 释放左键")
        print("  mouse.move,100,50     - 移动鼠标")
        print("  mouse.scroll,0,0,3    - 向上滚动")
        print("================\n")
    
    def run_demo(self):
        """运行演示序列"""
        print("\n🎮 运行演示序列...")
        
        demo_commands = [
            ("key.a", "输入字母a"),
            ("key.space", "按空格"),
            ("key.ctrl+a", "全选"),
            ("key.ctrl+c", "复制"),
            ("mouse.click,left", "左键点击"),
            ("mouse.move,100,50", "移动鼠标"),
            ("mouse.scroll,0,0,3", "向上滚动"),
            ("mouse.click,side1", "侧键1点击 (后退)"),
            ("mouse.click,side2", "侧键2点击 (前进)"),
            ("key.ctrl+v", "粘贴"),
        ]
        
        for cmd, desc in demo_commands:
            print(f"执行: {desc}")
            self.send_command(cmd)
            time.sleep(1.5)
        
        print("✅ 演示完成!")

def main():
    if len(sys.argv) < 2:
        print("用法: python esp32_client.py <ESP32_IP> [port] [mode]")
        print("示例:")
        print("  python esp32_client.py ***************")
        print("  python esp32_client.py *************** 8080")
        print("  python esp32_client.py *************** 8080 demo")
        print("")
        print("模式:")
        print("  interactive (默认) - 交互模式")
        print("  demo              - 演示模式")
        sys.exit(1)
    
    esp32_ip = sys.argv[1]
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 8080
    mode = sys.argv[3] if len(sys.argv) > 3 else "interactive"
    
    print(f"🚀 ESP32-S3 客户端")
    print(f"目标: {esp32_ip}:{port}")
    
    client = ESP32Client(esp32_ip, port)
    
    if client.connect():
        if mode == "demo":
            client.run_demo()
        else:
            client.interactive_mode()
    else:
        print("\n请确保:")
        print("1. ESP32-S3已连接到WiFi")
        print("2. ESP32-S3固件正在运行")
        print("3. IP地址正确")
        print("4. 防火墙允许连接")

if __name__ == "__main__":
    main()
