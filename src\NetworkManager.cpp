#include "NetworkManager.h"

void NetworkManager::init(const char* wifi_ssid, const char* wifi_password, int srv_port) {
    ssid = wifi_ssid;
    password = wifi_password;
    server_port = srv_port;

    connectToWiFi();

    // Initialize server after WiFi connection
    if (WiFi.status() == WL_CONNECTED) {
        server = new WiFiServer(server_port);
        server->begin();
        Serial.println("Server started on port " + String(server_port));
        Serial.println("Server IP: " + WiFi.localIP().toString());
    }
}

void NetworkManager::connectToWiFi() {
    Serial.println("Connecting to WiFi...");
    WiFi.begin(ssid, password);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.println("WiFi connected!");
        Serial.print("IP address: ");
        Serial.println(WiFi.localIP());
    } else {
        Serial.println();
        Serial.println("WiFi connection failed!");
    }
}

void NetworkManager::handleClientConnections() {
    if (WiFi.status() != WL_CONNECTED || !server) {
        return;
    }

    // Check for new client connections
    if (!clientConnected) {
        client = server->available();
        if (client) {
            Serial.println("Client connected: " + client.remoteIP().toString());
            clientConnected = true;
        }
    } else {
        // Check if current client is still connected
        if (!client.connected()) {
            Serial.println("Client disconnected");
            clientConnected = false;
        }
    }
}

void NetworkManager::handleConnection() {
    // Check WiFi connection
    if (WiFi.status() != WL_CONNECTED) {
        unsigned long currentTime = millis();
        if (currentTime - lastConnectionAttempt > connectionRetryInterval) {
            Serial.println("WiFi disconnected, attempting to reconnect...");
            connectToWiFi();
            // Restart server after WiFi reconnection
            if (WiFi.status() == WL_CONNECTED && !server) {
                server = new WiFiServer(server_port);
                server->begin();
                Serial.println("Server restarted on port " + String(server_port));
            }
            lastConnectionAttempt = currentTime;
        }
        return;
    }

    // Handle client connections
    handleClientConnections();

    // Read data from client
    if (clientConnected) {
        readFromClient();
    }
}

void NetworkManager::readFromClient() {
    if (client.available()) {
        String line = client.readStringUntil('\n');
        line.trim();

        if (line.length() > 0) {
            receivedCommand = line;
            Serial.println("Received from client: " + line);
        }
    }
}

String NetworkManager::getReceivedCommand() {
    String command = receivedCommand;
    receivedCommand = "";  // Clear after reading
    return command;
}

bool NetworkManager::isConnected() {
    return clientConnected && client.connected() && WiFi.status() == WL_CONNECTED;
}

String NetworkManager::getIPAddress() {
    return WiFi.localIP().toString();
}
