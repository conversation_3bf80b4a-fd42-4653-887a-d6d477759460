#include "NetworkManager.h"

void NetworkManager::init(const char* wifi_ssid, const char* wifi_password, 
                         const char* srv_ip, int srv_port) {
    ssid = wifi_ssid;
    password = wifi_password;
    server_ip = srv_ip;
    server_port = srv_port;
    
    connectToWiFi();
}

void NetworkManager::connectToWiFi() {
    Serial.println("Connecting to WiFi...");
    WiFi.begin(ssid, password);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.println("WiFi connected!");
        Serial.print("IP address: ");
        Serial.println(WiFi.localIP());
    } else {
        Serial.println();
        Serial.println("WiFi connection failed!");
    }
}

void NetworkManager::connectToServer() {
    if (WiFi.status() != WL_CONNECTED) {
        return;
    }
    
    if (!client.connected()) {
        Serial.println("Connecting to server...");
        if (client.connect(server_ip, server_port)) {
            Serial.println("Connected to server!");
            connected = true;
        } else {
            Serial.println("Failed to connect to server");
            connected = false;
        }
    }
}

void NetworkManager::handleConnection() {
    // Check WiFi connection
    if (WiFi.status() != WL_CONNECTED) {
        unsigned long currentTime = millis();
        if (currentTime - lastConnectionAttempt > connectionRetryInterval) {
            Serial.println("WiFi disconnected, attempting to reconnect...");
            connectToWiFi();
            lastConnectionAttempt = currentTime;
        }
        return;
    }
    
    // Check server connection
    if (!client.connected()) {
        unsigned long currentTime = millis();
        if (currentTime - lastConnectionAttempt > connectionRetryInterval) {
            connectToServer();
            lastConnectionAttempt = currentTime;
        }
        return;
    }
    
    // Read data from server
    readFromServer();
}

void NetworkManager::readFromServer() {
    if (client.available()) {
        String line = client.readStringUntil('\n');
        line.trim();
        
        if (line.length() > 0) {
            receivedCommand = line;
            Serial.println("Received from server: " + line);
        }
    }
}

String NetworkManager::getReceivedCommand() {
    String command = receivedCommand;
    receivedCommand = "";  // Clear after reading
    return command;
}

bool NetworkManager::isConnected() {
    return connected && client.connected() && WiFi.status() == WL_CONNECTED;
}
